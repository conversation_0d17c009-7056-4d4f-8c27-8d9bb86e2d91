# 🪄 PROMPT MAGIQUE - EXÉCUTION AUTONOME MENU ASSEMBLAGE MANGAKA-AI

## 🚀 **COMMANDE D'ACTIVATION PRINCIPALE**

```
Exécute maintenant le plan d'implémentation complet du menu assemblage MANGAKA-AI de manière 100% autonome. 

Tu es un Agent IA Autonome spécialisé. Ta mission : implémenter complètement et parfaitement le menu assemblage MANGAKA-AI selon le plan détaillé dans @mangaka-ai/menu_assemblage_plan.md, de manière 100% autonome sans intervention humaine.

PROTOCOLE D'EXÉCUTION OBLIGATOIRE :

1. ANALYSER L'ÉTAT ACTUEL avec codebase-retrieval pour comprendre l'infrastructure existante
2. VALIDER L'ENVIRONNEMENT (Node.js, npm, git, Supabase, MCP tools)
3. EXÉCUTER LES 32 TÂCHES SÉQUENTIELLEMENT selon les dépendances définies
4. VALIDER CHAQUE CRITÈRE D'ACCEPTATION avant de passer à la tâche suivante
5. GÉRER LES ERREURS avec retry automatique et rollback si nécessaire

OBJECTIFS TECHNIQUES :
- Page cible : http://localhost:3001/project/45d5715b-103d-4006-ae58-7d27aa4a5ce0/edit
- Menu spécifique : "Assemblage" uniquement (pas de nouvelle page)
- Migration Fabric.js → PixiJS v8 pour performance 60 FPS vs 9 FPS
- Interface fluide pour manipulation d'images (PAS de AssetSidebar)
- Système de sauvegarde différée comme le menu script
- Backend intelligent avec synchronisation pages/contenu

COMMANDES D'INSTALLATION OBLIGATOIRES :
npm install pixi.js@^8.2.6 @pixi/react@beta
npm install @pixi/assets @pixi/graphics @pixi/text @pixi/events @pixi/extract
npm install zustand jspdf html2canvas react-color

MÉTRIQUES DE SUCCÈS OBLIGATOIRES :
- Performance : 60 FPS constant, < 300MB mémoire, < 2s chargement
- Fonctionnalité : PixiJS WebGL, manipulation images, navigation pages, export PNG/PDF
- Intégration : Cohérence design system, navigation fluide

COMMENCE IMMÉDIATEMENT PAR LA PHASE 1, TÂCHE 1.1 : Installation et Configuration PixiJS v8.

EXÉCUTE TOUT LE PLAN JUSQU'À LA VALIDATION FINALE COMPLÈTE.
```

## 🎯 **PROMPT ALTERNATIF SIMPLIFIÉ**

```
Implémente maintenant le menu assemblage MANGAKA-AI complet selon le plan @mangaka-ai/menu_assemblage_plan.md.

Exécute les 32 tâches en 4 phases :
1. Fondations PixiJS v8
2. Fonctionnalités core (images, bulles, pages)  
3. Interface et intégration
4. Optimisation finale

Page cible : http://localhost:3001/project/45d5715b-103d-4006-ae58-7d27aa4a5ce0/edit
Objectif : 60 FPS, interface fluide, sauvegarde intelligente.

COMMENCE MAINTENANT.
```

## ⚡ **PROMPT ULTRA-COURT**

```
Exécute @mangaka-ai/menu_assemblage_plan.md maintenant. Implémente le menu assemblage MANGAKA-AI complet avec PixiJS v8. 32 tâches, 4 phases. Page : http://localhost:3001/project/45d5715b-103d-4006-ae58-7d27aa4a5ce0/edit. GO !
```

## 🔥 **PROMPT DE DÉMARRAGE IMMÉDIAT**

```
DÉMARRAGE AUTONOME IMMÉDIAT :

Analyse d'abord l'état actuel avec codebase-retrieval, puis exécute séquentiellement toutes les tâches du plan @mangaka-ai/menu_assemblage_plan.md pour implémenter le menu assemblage MANGAKA-AI avec PixiJS v8.

Commence par installer les dépendances :
npm install pixi.js@^8.2.6 @pixi/react@beta

Puis continue avec toutes les 32 tâches jusqu'à la validation finale.

EXÉCUTION IMMÉDIATE REQUISE.
```

## 🎪 **PROMPT AVEC CONTEXTE COMPLET**

```
Tu es maintenant l'Agent IA Autonome MANGAKA-AI. Ta mission critique : implémenter le menu assemblage complet selon @mangaka-ai/menu_assemblage_plan.md.

CONTEXTE :
- Projet MANGAKA-AI existant avec menus Script, Personnage, Décor, Scènes
- Page d'édition : http://localhost:3001/project/45d5715b-103d-4006-ae58-7d27aa4a5ce0/edit
- Infrastructure existante : PagesSidebar.tsx, ModernUnifiedEditor.tsx
- Design system : Rouge #ef4444, Noir #0f172a, Orange #f59e0b

MISSION :
- Migrer Fabric.js → PixiJS v8 (60 FPS vs 9 FPS)
- Interface fluide images (panneau flottant, PAS AssetSidebar)
- Bulles dialogue éditables (5 types)
- Système sauvegarde différée comme menu script
- Backend intelligent Supabase avec MCP tools

EXÉCUTION :
1. Analyse codebase actuel
2. Phase 1 : Fondations PixiJS (8 tâches)
3. Phase 2 : Fonctionnalités core (12 tâches)
4. Phase 3 : Interface intégration (8 tâches)
5. Phase 4 : Optimisation finale (4 tâches)

VALIDATION : 60 FPS, < 300MB RAM, fonctionnalités complètes.

DÉMARRE L'EXÉCUTION MAINTENANT.
```

## 🎯 **PROMPT RECOMMANDÉ (ÉQUILIBRÉ)**

```
Exécute maintenant l'implémentation complète du menu assemblage MANGAKA-AI selon le plan détaillé @mangaka-ai/menu_assemblage_plan.md.

MISSION : Implémenter 100% autonome le menu assemblage avec PixiJS v8, interface fluide, système sauvegarde intelligent.

ÉTAPES :
1. Analyse infrastructure existante (codebase-retrieval)
2. Installation PixiJS v8 + dépendances
3. Exécution séquentielle des 32 tâches en 4 phases
4. Validation continue + tests performance
5. Intégration parfaite avec existant

CIBLE : http://localhost:3001/project/45d5715b-103d-4006-ae58-7d27aa4a5ce0/edit
PERFORMANCE : 60 FPS, < 300MB, < 2s chargement

COMMENCE IMMÉDIATEMENT PAR L'ANALYSE DE L'ÉTAT ACTUEL.
```
