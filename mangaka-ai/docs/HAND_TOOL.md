# Outil Main (Hand Tool) - Documentation

## Vue d'ensemble

L'outil Main permet de naviguer librement dans le canvas avec des fonctionnalités de pan (déplacement) et de zoom, similaire aux éditeurs graphiques professionnels comme Photoshop, Figma ou Adobe Illustrator.

## Fonctionnalités

### 🖱️ Pan (Déplacement)
- **Activation** : Sélectionner l'outil Main dans la toolbar (icône main)
- **Utilisation** : Cliquer-glisser pour déplacer la vue du canvas
- **Curseur** : Change automatiquement en icône "grab" (main ouverte) ou "grabbing" (main fermée)
- **Limites** : Aucune limite stricte, mais garde une partie du canvas visible

### 🔍 Zoom avec molette
- **Zoom In** : Molette vers le haut
- **Zoom Out** : Molette vers le bas
- **Centrage** : Le zoom se fait en direction du pointeur de la souris
- **Synchronisation** : Synchronisé avec le système de zoom global (zoomLevel dans PolotnoContext)

### 🎯 Intégration
- **Toolbar** : Bouton dédié dans PolotnoVerticalToolbar.tsx
- **Raccourci** : Touche `H`
- **État** : Géré comme les autres outils (sélectionnable/désélectionnable)

## Architecture technique

### Composants modifiés

#### 1. `types/polotno.types.ts`
```typescript
export type PolotnoTool = 'select' | 'text' | 'rectangle' | 'circle' | 'image' | 'panel' | 'bubble' | 'grid' | 'hand'
```

#### 2. `layout/PolotnoVerticalToolbar.tsx`
- Ajout du bouton outil main avec icône `Hand` de Lucide
- Gestion de l'état actif
- Raccourci clavier `H`

#### 3. `core/SimpleCanvasEditor.tsx`
- États pour le pan et la transformation du canvas
- Gestionnaires d'événements souris (mouseDown, mouseMove, mouseUp)
- Gestionnaire d'événement molette (wheel)
- Synchronisation avec le zoom du contexte

### États internes

```typescript
// État pour l'outil main (pan)
const [panState, setPanState] = useState({
  isPanning: false,
  startX: 0,
  startY: 0,
  startTransformX: 0,
  startTransformY: 0
})

// État de transformation du canvas
const [canvasTransform, setCanvasTransform] = useState({
  x: 0,
  y: 0,
  scale: 1
})
```

### Logique de fonctionnement

#### Pan (Déplacement)
1. **MouseDown** : Démarrer le pan si l'outil main est actif
2. **MouseMove** : Calculer le delta et mettre à jour la transformation
3. **MouseUp** : Arrêter le pan

#### Zoom
1. **Wheel Event** : Détecter la direction de la molette
2. **Zoom In/Out** : Appeler les fonctions du contexte (`zoomIn`, `zoomOut`)
3. **Synchronisation** : Le scale est automatiquement synchronisé via `useEffect`

## Utilisation

### Activation de l'outil
```typescript
// Via la toolbar
onToolChange('hand')

// Via le contexte
setActiveTool('hand')
```

### Vérification de l'état
```typescript
const isHandToolActive = activeTool === 'hand'
```

### Curseurs
- **Repos** : `cursor: 'grab'`
- **Pan actif** : `cursor: 'grabbing'`
- **Transition** : Automatique selon l'état

## Styles CSS appliqués

```css
/* Canvas avec transformation */
transform: translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasScale});
transform-origin: center;
transition: ${panState.isPanning ? 'none' : 'transform 0.2s ease'};
```

## Événements gérés

### Souris
- `onMouseDown` : Démarrer le pan
- `onMouseMove` : Continuer le pan + mise à jour curseur
- `onMouseUp` : Arrêter le pan

### Molette
- `onWheel` : Zoom in/out avec preventDefault pour éviter le scroll de page

## Synchronisation avec le contexte

### Zoom
```typescript
// Synchroniser le scale avec le zoomLevel du contexte
useEffect(() => {
  setCanvasTransform(prev => ({
    ...prev,
    scale: canvasScale
  }))
}, [zoomLevel, canvasScale])
```

### Transformation
```typescript
// Notifier les changements de transformation
useEffect(() => {
  onCanvasTransformChange?.({
    x: canvasTransform.x,
    y: canvasTransform.y,
    scale: canvasScale
  })
}, [canvasTransform, canvasScale])
```

## Tests

### Tests unitaires
- Rendu du canvas avec l'outil main
- Changement de curseur
- Gestion du pan (drag)
- Gestion du zoom (molette)
- Synchronisation avec le contexte
- Limites et contraintes

### Tests d'intégration
- Interaction avec les autres outils
- Synchronisation toolbar ↔ canvas
- Performance avec de nombreux éléments

## Limitations actuelles

1. **Limites de pan** : Pas de contraintes strictes pour garder le canvas visible
2. **Zoom centré** : Le zoom se fait toujours au centre, pas sur le curseur
3. **Performance** : Pas d'optimisation spécifique pour de très gros canvas

## Améliorations futures

1. **Zoom centré sur curseur** : Calculer la position du curseur pour centrer le zoom
2. **Limites intelligentes** : Empêcher de perdre complètement le canvas
3. **Inertie** : Ajouter de l'inertie au pan pour une sensation plus fluide
4. **Mini-map** : Ajouter une mini-carte pour la navigation
5. **Raccourcis** : Zoom avec Ctrl+molette, pan avec espace+drag

## Compatibilité

- ✅ React 19
- ✅ TypeScript
- ✅ Tous navigateurs modernes
- ✅ Touch devices (avec adaptations)

## Performance

- **Optimisé** : Utilisation de `useCallback` pour éviter les re-renders
- **Transitions** : Désactivées pendant le pan pour la fluidité
- **Transform CSS** : Utilisation de `transform` pour les performances GPU
