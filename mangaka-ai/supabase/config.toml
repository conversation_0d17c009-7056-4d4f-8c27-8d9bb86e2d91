# MANGAKA AI - Supabase Configuration

project_id = "mangaka-ai"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54324
site_url = "http://localhost:3001"
additional_redirect_urls = ["https://mangaka-ai.vercel.app"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true

[db]
port = 54322
shadow_port = 54320
major_version = 15

[realtime]
enabled = true
port = 54323

[studio]
enabled = true
port = 54323

[inbucket]
enabled = true
port = 54324

[storage]
enabled = true
port = 54325
file_size_limit = "50MiB"
image_transformation = { enabled = true }

[edge_functions]
enabled = true
port = 54326

[analytics]
enabled = false
