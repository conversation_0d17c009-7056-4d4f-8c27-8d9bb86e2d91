# 📝 Guide d'Utilisation - Éditeur de Script MANGAKA AI

## 🎯 Introduction

L'éditeur de script MANGAKA AI a été complètement révolutionné pour offrir l'expérience d'écriture la plus professionnelle possible. Inspiré des meilleurs éditeurs de code, il combine simplicité et puissance pour créer des scripts manga de qualité professionnelle.

## 🚀 Démarrage Rapide

### 1. Accès à l'Éditeur
- Naviguez vers votre projet MANGAKA AI
- Cliquez sur l'onglet "Script" dans l'éditeur unifié
- L'interface s'ouvre avec un script vide prêt à l'emploi

### 2. Premier Script
Commencez à taper directement dans la zone d'écriture :

```
PAGE 1

PANEL 1
(<PERSON> se réveille dans sa chambre, les rayons du soleil filtrent par la fenêtre)
AKIRA: Encore ce rêve étrange...

PANEL 2
(Il se lève et regarde par la fenêtre)
AKIRA: Qu'est-ce que ça peut bien signifier ?

PAGE 2
...
```

## 🎨 Interface Utilisateur

### Sidebar Gauche (Navigation)
- **Titre du Script** : Cliquez pour modifier le nom
- **Recherche** : Trouvez rapidement du texte dans votre script
- **Statistiques Live** : Pages, panels, mots, caractères en temps réel
- **Personnages** : Liste automatique des personnages détectés
- **Export** : Boutons TXT et JSON pour exporter votre travail
- **Sauvegarde** : Indicateur de statut avec timestamp

### Zone d'Écriture Centrale
- **Numéros de ligne** : Affichage automatique et synchronisé
- **Coloration syntaxique** : Couleurs différentes selon le type de contenu
- **Écriture fluide** : Interface type éditeur de code professionnel
- **Format automatique** : Détection intelligente du contenu

### Barre d'Aide (Bas)
- **Aide format** : Rappel des conventions d'écriture
- **Raccourcis** : Liste des raccourcis clavier disponibles

## 📋 Format de Script Recommandé

### Structure Standard
```
PAGE [numéro]

PANEL [numéro]
(Description de l'action/scène)
PERSONNAGE: Dialogue du personnage

PANEL [numéro]
(Nouvelle action)
AUTRE_PERSONNAGE: Autre dialogue
```

### Types de Contenu Reconnus

#### 🔴 Pages (Rouge, Gras)
```
PAGE 1
Page 2
PAGE 15
```

#### 🟡 Panels (Jaune, Semi-Gras)
```
PANEL 1
Panel 2
PANEL 15
```

#### 🔵 Dialogues (Bleu)
```
AKIRA: Bonjour tout le monde !
SAKURA: Comment ça va ?
NARRATEUR: Il était une fois...
```

#### ⚪ Descriptions (Gris)
```
(Akira court dans la rue)
(Gros plan sur le visage de Sakura)
(Vue panoramique de la ville)
```

## ⚡ Fonctionnalités Avancées

### 1. Détection Automatique des Personnages
- Tapez `PERSONNAGE:` et le nom sera automatiquement ajouté à la liste
- Consultez la liste dans la sidebar sous "Personnages"
- Cliquez pour développer/réduire la liste

### 2. Statistiques en Temps Réel
- **Pages** : Nombre total de pages dans votre script
- **Panels** : Nombre total de panels
- **Mots** : Comptage automatique des mots
- **Caractères** : Longueur totale du script

### 3. Auto-Sauvegarde
- Sauvegarde automatique toutes les 30 secondes
- Indicateur visuel dans la sidebar
- Timestamp de la dernière sauvegarde
- Sauvegarde manuelle avec Ctrl+S

### 4. Export Professionnel
- **TXT** : Format standard de l'industrie
- **JSON** : Données structurées pour développeurs
- Noms de fichiers automatiques basés sur le titre

## ⌨️ Raccourcis Clavier

| Raccourci | Action |
|-----------|--------|
| `Ctrl+S` | Sauvegarde manuelle |
| `Ctrl+F` | Recherche (à venir) |
| `Tab` | Indentation |
| `Shift+Tab` | Désindentation |

## 💡 Conseils d'Écriture

### 1. Structure Claire
- Commencez toujours par `PAGE 1`
- Numérotez vos panels séquentiellement
- Utilisez des descriptions concises entre parenthèses

### 2. Dialogues Efficaces
- Format : `PERSONNAGE: Dialogue`
- Noms en MAJUSCULES pour la cohérence
- Dialogues courts et percutants

### 3. Descriptions Visuelles
- Décrivez l'action, pas les émotions
- Pensez "caméra" : plans, angles, mouvements
- Soyez précis mais concis

### 4. Organisation
- Une page = 4-8 panels en moyenne
- Variez les types de plans
- Pensez au rythme de lecture

## 🔧 Dépannage

### Script ne se sauvegarde pas
- Vérifiez votre connexion internet
- L'indicateur rouge signale un problème
- Utilisez Ctrl+S pour forcer la sauvegarde

### Personnages non détectés
- Vérifiez le format : `PERSONNAGE:`
- Assurez-vous qu'il n'y a pas d'espaces avant le nom
- Le nom doit être suivi de deux points

### Coloration incorrecte
- Respectez le format exact (PAGE, PANEL, etc.)
- Les parenthèses doivent entourer complètement la description
- Rechargez la page si nécessaire

## 🎯 Bonnes Pratiques

### 1. Planification
- Esquissez votre histoire avant d'écrire
- Définissez vos personnages principaux
- Planifiez le nombre de pages approximatif

### 2. Écriture
- Écrivez d'abord, éditez ensuite
- Utilisez les statistiques pour suivre votre progression
- Sauvegardez régulièrement (auto-sauvegarde activée)

### 3. Révision
- Relisez à voix haute les dialogues
- Vérifiez la cohérence des noms de personnages
- Exportez en TXT pour une révision finale

## 🚀 Prochaines Fonctionnalités

- **Recherche et remplacement** avancée
- **Thèmes personnalisables** (clair/sombre)
- **Collaboration temps réel**
- **Import de scripts** existants
- **Suggestions IA** pour l'écriture

---

**Besoin d'aide ?** L'interface est conçue pour être intuitive. Commencez à taper et laissez la magie opérer ! 🎨✨
