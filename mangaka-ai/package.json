{"name": "mangaka-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "dev:turbo": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "test-atomic-deletion": "node scripts/test-atomic-deletion.js"}, "dependencies": {"@hufe921/canvas-editor": "^0.9.111", "@pixi/assets": "^7.4.3", "@pixi/events": "^7.4.3", "@pixi/extract": "^7.4.3", "@pixi/graphics": "^7.4.3", "@pixi/react": "^8.0.0-beta.25", "@pixi/text": "^7.4.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fabric": "^6.6.6", "html2canvas": "^1.4.1", "immer": "^10.1.1", "jspdf": "^3.0.1", "konva": "^9.3.20", "lucide-react": "^0.511.0", "next": "15.3.2", "pixi.js": "^8.5.2", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-konva": "^19.0.5", "sharp": "^0.34.2", "tailwind-merge": "^3.3.0", "use-image": "^1.1.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}