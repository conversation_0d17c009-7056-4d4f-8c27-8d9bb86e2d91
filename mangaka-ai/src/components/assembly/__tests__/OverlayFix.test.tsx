import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import PolotnoAssemblyApp from '../PolotnoAssemblyApp'

// Mock des dépendances
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

jest.mock('../core/SimpleCanvasEditor', () => {
  return function MockSimpleCanvasEditor(props: any) {
    return (
      <div 
        data-testid="simple-canvas-editor"
        onClick={() => props.onCanvasClick?.(100, 100)}
        style={{ width: props.width, height: props.height }}
      >
        Canvas Editor Mock
      </div>
    )
  }
})

describe('Overlay Fix - PolotnoAssemblyApp', () => {
  const defaultProps = {
    projectId: 'test-project',
    currentPage: 1
  }

  it('should have proper padding to avoid left overlay zone', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    // Trouver le conteneur du canvas avec le padding corrigé
    const canvasContainer = document.querySelector('.canvas-interface')
    
    expect(canvasContainer).toHaveClass('pl-20') // 80px padding left
    expect(canvasContainer).toHaveClass('pr-8')  // 32px padding right
    expect(canvasContainer).toHaveClass('py-8')  // 32px padding top/bottom
  })

  it('should not have uniform padding that creates overlay zones', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    const canvasContainer = document.querySelector('.canvas-interface')
    
    // Vérifier qu'on n'a pas p-8 qui créait le problème
    expect(canvasContainer).not.toHaveClass('p-8')
  })

  it('should have left toolbar with proper pointer-events settings', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    // Vérifier que la sidebar gauche a pointer-events-none sur le conteneur
    const leftToolbarContainer = document.querySelector('.absolute.left-0.w-16')
    expect(leftToolbarContainer).toHaveClass('pointer-events-none')

    // Et pointer-events-auto sur le contenu
    const toolbarContent = document.querySelector('.pointer-events-auto')
    expect(toolbarContent).toBeInTheDocument()
  })

  it('should allow canvas clicks without interference from left overlay', () => {
    const mockCanvasClick = jest.fn()
    
    // Mock du SimpleCanvasEditor pour capturer les clics
    jest.doMock('../core/SimpleCanvasEditor', () => {
      return function MockSimpleCanvasEditor(props: any) {
        return (
          <div 
            data-testid="simple-canvas-editor"
            onClick={() => {
              mockCanvasClick()
              props.onCanvasClick?.(100, 100)
            }}
            style={{ width: props.width, height: props.height }}
          >
            Canvas Editor Mock
          </div>
        )
      }
    })

    render(<PolotnoAssemblyApp {...defaultProps} />)

    const canvasEditor = screen.getByTestId('simple-canvas-editor')
    fireEvent.click(canvasEditor)

    expect(mockCanvasClick).toHaveBeenCalled()
  })

  it('should have canvas container positioned correctly relative to sidebars', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    const canvasContainer = document.querySelector('.canvas-container')
    const leftSidebar = document.querySelector('.absolute.left-0.w-16')
    const rightSidebar = document.querySelector('.absolute.right-0.w-80')

    // Vérifier que les sidebars sont en position absolute
    expect(leftSidebar).toHaveClass('absolute', 'left-0', 'z-50')
    expect(rightSidebar).toHaveClass('absolute', 'right-0', 'z-50')

    // Vérifier que le canvas container a les bonnes classes
    expect(canvasContainer).toHaveClass('bg-black', 'shadow-2xl', 'rounded-lg')
  })

  it('should have proper z-index hierarchy', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    const leftSidebar = document.querySelector('.absolute.left-0.w-16')
    const rightSidebar = document.querySelector('.absolute.right-0.w-80')
    const canvasArea = document.querySelector('.absolute.inset-0.bg-black')

    // Sidebars doivent avoir z-50, canvas z-10
    expect(leftSidebar).toHaveClass('z-50')
    expect(rightSidebar).toHaveClass('z-50')
    expect(canvasArea).toHaveClass('z-10')
  })

  it('should have toolbar buttons still functional', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    // Vérifier que les boutons de la toolbar sont présents et cliquables
    const selectButton = document.querySelector('button[title*="Sélection"]')
    const panelButton = document.querySelector('button[title*="Panel"]')

    expect(selectButton).toBeInTheDocument()
    expect(panelButton).toBeInTheDocument()

    if (selectButton) {
      fireEvent.click(selectButton)
      // Le bouton devrait être cliquable sans problème
    }
  })

  it('should not have any elements creating dead zones in left area', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    // Vérifier que le padding left de 80px laisse assez d'espace pour la sidebar de 64px
    const canvasInterface = document.querySelector('.canvas-interface')
    
    if (canvasInterface) {
      const styles = window.getComputedStyle(canvasInterface)
      const paddingLeft = parseInt(styles.paddingLeft)
      
      // 80px (pl-20) devrait être suffisant pour éviter la sidebar de 64px
      expect(paddingLeft).toBeGreaterThanOrEqual(64)
    }
  })

  it('should maintain canvas visibility across full width when dragged', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    // Vérifier que la zone de canvas couvre bien toute la largeur disponible
    const canvasArea = document.querySelector('.absolute.inset-0.bg-black')
    
    expect(canvasArea).toHaveClass('inset-0')
    
    if (canvasArea) {
      const rect = canvasArea.getBoundingClientRect()
      expect(rect.left).toBe(0)
      expect(rect.right).toBeGreaterThan(0)
    }
  })

  it('should have proper overflow settings to prevent scrollbars', () => {
    render(<PolotnoAssemblyApp {...defaultProps} />)

    const canvasInterface = document.querySelector('.canvas-interface')
    const mainLayout = document.querySelector('.overflow-hidden')

    expect(canvasInterface).toHaveClass('no-scrollbar')
    expect(mainLayout).toHaveClass('overflow-hidden')
  })
})
