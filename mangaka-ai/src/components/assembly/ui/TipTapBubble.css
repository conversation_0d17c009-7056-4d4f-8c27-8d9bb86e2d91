/* Styles pour le nouveau système TipTapBubble */

.tiptap-bubble {
  /* Base styles déjà dans Tailwind */
  position: relative;
}

/* Styles pour l'éditeur TipTap */
.tiptap-bubble-editor {
  /* Styles TipTap intégrés */
}

.tiptap-bubble-editor .ProseMirror {
  outline: none !important;
  border: none !important;
  background: transparent;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 8px;
  margin: 0;
  min-height: 40px;
}

.tiptap-bubble-editor .ProseMirror p {
  margin: 0;
  padding: 0;
  line-height: 1.3;
  font-family: 'Comic Sans MS', 'Bangers', 'Roboto', system-ui, sans-serif !important;
  font-weight: 700 !important;
  font-size: 20px !important;
  color: #000000 !important;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.8);
}

.tiptap-bubble-editor .ProseMirror:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Styles pour le texte affiché en mode lecture */
.tiptap-bubble-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  padding: 8px;
  line-height: 1.3;
  color: #000000 !important;
  font-family: 'Comic Sans MS', 'Bangers', 'Roboto', system-ui, sans-serif !important;
  font-weight: 700 !important;
  font-size: 20px !important;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.8);
}

/* ✅ STYLES OPTIMISÉS POUR TOUS LES ÉLÉMENTS DE TEXTE */
.tiptap-bubble-text *,
.tiptap-bubble-text p,
.tiptap-bubble-text span,
.tiptap-bubble-text div {
  color: #000000 !important;
  font-family: 'Comic Sans MS', 'Bangers', 'Roboto', system-ui, sans-serif !important;
  font-weight: 700 !important;
  font-size: 20px !important;
  line-height: 1.3 !important;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.8);
}

/* ✅ OVERRIDE ULTRA-AGRESSIF AVEC STYLES COMIC */
[data-bubble-id] *,
[data-bubble-id] p,
[data-bubble-id] span,
[data-bubble-id] div {
  color: #000000 !important;
  font-family: 'Comic Sans MS', 'Bangers', 'Roboto', system-ui, sans-serif !important;
  font-weight: 700 !important;
  font-size: 20px !important;
  line-height: 1.3 !important;
}

/* ✅ STYLES SPÉCIFIQUES PAR TYPE DE BULLE */
[data-bubble-type="shout"] .tiptap-bubble-text,
[data-bubble-type="shout"] .tiptap-bubble-text * {
  font-weight: 900 !important;
  font-size: 22px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.9);
}

[data-bubble-type="thought"] .tiptap-bubble-text,
[data-bubble-type="thought"] .tiptap-bubble-text * {
  font-style: italic !important;
  font-weight: 600 !important;
  font-size: 19px !important;
}

[data-bubble-type="whisper"] .tiptap-bubble-text,
[data-bubble-type="whisper"] .tiptap-bubble-text * {
  font-size: 18px !important;
  font-weight: 600 !important;
  opacity: 0.9 !important;
}

/* Styles pour les différents modes */
.tiptap-bubble[data-bubble-mode="reading"] {
  /* Mode lecture - style par défaut */
}

.tiptap-bubble[data-bubble-mode="editing"] {
  /* Mode édition - SANS ombre */
}

.tiptap-bubble[data-bubble-mode="manipulating"] {
  /* Mode manipulation - SANS ombre verte */
}

/* Styles pour les différents types de bulles */
.tiptap-bubble[data-bubble-type="speech"] {
  /* Bulle de dialogue classique */
}

.tiptap-bubble[data-bubble-type="thought"] {
  /* Bulle de pensée */
}

.tiptap-bubble[data-bubble-type="shout"] {
  /* Bulle de cri */
  font-weight: bold;
}

.tiptap-bubble[data-bubble-type="whisper"] {
  /* Bulle de chuchotement */
  font-style: italic;
}

.tiptap-bubble[data-bubble-type="explosion"] {
  /* Bulle d'explosion */
  font-weight: bold;
  text-transform: uppercase;
}

/* Animations et transitions */
.tiptap-bubble {
  transition: all 0.2s ease-out;
}

.tiptap-bubble:hover {
  /* ✅ SUPPRIMÉ : Plus d'effet de zoom au survol */
}

.tiptap-bubble[data-bubble-mode="editing"]:hover {
  transform: none; /* Pas d'animation en mode édition */
}

/* Styles pour les queues de bulles (à implémenter) */
.tiptap-bubble::before {
  content: '';
  position: absolute;
  /* Queue sera implémentée avec CSS ou SVG */
}

/* Responsive */
@media (max-width: 768px) {
  .tiptap-bubble {
    min-width: 80px;
    min-height: 40px;
  }
  
  .tiptap-bubble-editor .ProseMirror {
    padding: 6px;
    min-height: 30px;
  }
  
  .tiptap-bubble-text {
    padding: 6px;
  }
}

/* Accessibilité */
.tiptap-bubble:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Styles pour les handles de manipulation (à ajouter plus tard) */
.tiptap-bubble-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tiptap-bubble[data-bubble-mode="manipulating"] .tiptap-bubble-handle {
  opacity: 1;
}

.tiptap-bubble-handle:hover {
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

/* Positions des handles */
.tiptap-bubble-handle.top-left {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.tiptap-bubble-handle.top-right {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.tiptap-bubble-handle.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.tiptap-bubble-handle.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}
