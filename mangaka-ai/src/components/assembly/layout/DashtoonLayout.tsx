'use client'

import React from 'react'
import { CssDotPattern } from '../ui/DotPattern'

interface DashtoonLayoutProps {
  leftToolbar: React.ReactNode
  centerCanvas: React.ReactNode
  rightPanel: React.ReactNode
  className?: string
}

/**
 * Layout principal reproduisant l'interface Dashtoon
 * - Barre d'outils verticale gauche (fine, ~60px)
 * - Zone centrale pour le canvas (fond distinctif)
 * - Menu droit dual-fonction (Pages/Images)
 */
export default function DashtoonLayout({
  leftToolbar,
  centerCanvas,
  rightPanel,
  className = ''
}: DashtoonLayoutProps) {
  return (
    <div className={`h-screen flex bg-dark-900 overflow-hidden relative ${className}`}>
      {/* Zone centrale - Canvas avec fond noir et motif décoratif (PLEIN ÉCRAN) */}
      <div className="absolute inset-0 bg-black overflow-hidden">
        {/* ✨ MOTIF DE POINTS DÉCORATIFS GLOBAL */}
        <CssDotPattern
          size={1.5}
          spacing={24}
          opacity={0.12}
          color="#ffffff"
          className="absolute inset-0 z-0"
        />

        <div className="absolute inset-0 z-10">
          {centerCanvas}
        </div>
      </div>

      {/* Barre d'outils gauche - TRANSPARENTE, ne bloque pas le canvas */}
      <div className="absolute left-0 top-0 bottom-0 w-16 bg-transparent z-50 pointer-events-none">
        <div className="pointer-events-auto bg-dark-800/90 backdrop-blur-sm border-r border-dark-700/50 h-full">
          {leftToolbar}
        </div>
      </div>

      {/* Menu droit - TRANSPARENTE, ne bloque pas le canvas */}
      <div className="absolute right-0 top-0 bottom-0 w-80 bg-transparent z-50 pointer-events-none">
        <div className="pointer-events-auto bg-dark-800/90 backdrop-blur-sm border-l border-dark-700/50 h-full">
          {rightPanel}
        </div>
      </div>
    </div>
  )
}
