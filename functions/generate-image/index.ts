import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface GenerateImageRequest {
  prompt: string
  type: 'character' | 'background' | 'scene'
  optimizePrompt?: boolean
}

interface XaiImageResponse {
  data: Array<{
    url: string
    revised_prompt?: string
  }>
}

// Prompt optimization templates for manga style
const MANGA_TEMPLATES = {
  character: "manga style, anime art, detailed character design, clean lines, professional illustration",
  background: "manga style, anime background, detailed environment, atmospheric, professional illustration",
  scene: "manga style, anime scene, dynamic composition, detailed illustration, professional artwork"
}

const QUALITY_ENHANCERS = "high quality, detailed, sharp, professional, 4k resolution"
const STYLE_CONSISTENCY = "consistent art style, manga aesthetic, black and white with selective color"

function optimizePrompt(originalPrompt: string, imageType: 'character' | 'background' | 'scene'): string {
  // Remove any existing style keywords to avoid conflicts
  let cleanPrompt = originalPrompt
    .replace(/\b(manga|anime|style|art)\b/gi, '')
    .replace(/\s+/g, ' ')
    .trim()

  // Add manga-specific template
  const template = MANGA_TEMPLATES[imageType]
  
  // Construct optimized prompt
  const optimizedPrompt = `${template}, ${cleanPrompt}, ${QUALITY_ENHANCERS}, ${STYLE_CONSISTENCY}`
  
  return optimizedPrompt
}

async function generateImageWithXai(prompt: string): Promise<string> {
  const XAI_API_KEY = Deno.env.get('XAI_API_KEY')
  const XAI_API_URL = Deno.env.get('XAI_API_URL') || 'https://api.x.ai/v1'
  
  if (!XAI_API_KEY) {
    throw new Error('XAI_API_KEY not configured')
  }

  const response = await fetch(`${XAI_API_URL}/images/generations`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${XAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'grok-2-vision',
      prompt: prompt,
      n: 1,
      size: '1024x1024',
      quality: 'hd',
      style: 'natural'
    }),
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`Xai API error: ${response.status} - ${errorText}`)
  }

  const data: XaiImageResponse = await response.json()
  
  if (!data.data || data.data.length === 0) {
    throw new Error('No image generated by Xai API')
  }

  return data.data[0].url
}

async function uploadImageToSupabase(imageUrl: string, userId: string, imageId: string): Promise<string> {
  // Download image from Xai
  const imageResponse = await fetch(imageUrl)
  if (!imageResponse.ok) {
    throw new Error('Failed to download image from Xai')
  }
  
  const imageBlob = await imageResponse.blob()
  const fileName = `${userId}/${imageId}.png`
  
  // Upload to Supabase Storage
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )
  
  const { data, error } = await supabase.storage
    .from('generated-images')
    .upload(fileName, imageBlob, {
      contentType: 'image/png',
      upsert: false
    })
  
  if (error) {
    throw new Error(`Failed to upload image: ${error.message}`)
  }
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from('generated-images')
    .getPublicUrl(fileName)
  
  return publicUrl
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Verify user
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { prompt, type, optimizePrompt = true }: GenerateImageRequest = await req.json()

    if (!prompt || !type) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: prompt, type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check user credits
    const { data: userCredits, error: creditsError } = await supabase
      .from('user_credits')
      .select('credits_remaining')
      .eq('user_id', user.id)
      .single()

    if (creditsError || !userCredits || userCredits.credits_remaining < 1) {
      return new Response(
        JSON.stringify({ error: 'Insufficient credits' }),
        { status: 402, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Optimize prompt if requested
    const finalPrompt = optimizePrompt ? optimizePrompt(prompt, type) : prompt
    
    // Generate image with Xai
    const startTime = Date.now()
    const imageUrl = await generateImageWithXai(finalPrompt)
    const generationTime = Date.now() - startTime

    // Generate unique ID for this image
    const imageId = crypto.randomUUID()
    
    // Upload image to Supabase Storage
    const publicUrl = await uploadImageToSupabase(imageUrl, user.id, imageId)

    // Save to database
    const { error: insertError } = await supabase
      .from('generated_images')
      .insert({
        id: imageId,
        user_id: user.id,
        original_prompt: prompt,
        optimized_prompt: finalPrompt,
        image_url: publicUrl,
        image_type: type,
        credits_used: 1,
        generation_time_ms: generationTime
      })

    if (insertError) {
      throw new Error(`Failed to save image record: ${insertError.message}`)
    }

    // Deduct credit
    const { error: updateError } = await supabase
      .from('user_credits')
      .update({ 
        credits_remaining: userCredits.credits_remaining - 1 
      })
      .eq('user_id', user.id)

    if (updateError) {
      console.error('Failed to update credits:', updateError)
      // Don't fail the request, but log the error
    }

    // Get updated credits
    const { data: updatedCredits } = await supabase
      .from('user_credits')
      .select('credits_remaining')
      .eq('user_id', user.id)
      .single()

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          imageId,
          imageUrl: publicUrl,
          originalPrompt: prompt,
          optimizedPrompt: finalPrompt,
          creditsUsed: 1,
          creditsRemaining: updatedCredits?.credits_remaining || 0,
          generationTimeMs: generationTime
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Generate image error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        success: false 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
